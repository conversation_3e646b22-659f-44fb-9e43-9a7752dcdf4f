#!/usr/bin/env python3
"""
Visualize OCR results with bounding boxes and translations overlaid on the image.
"""
import json
import sys
import os
from pathlib import Path

def install_dependencies():
    """Install required packages for visualization."""
    try:
        from PIL import Image, ImageDraw, ImageFont
        import matplotlib.pyplot as plt
        import matplotlib.patches as patches
    except ImportError:
        print("Installing required packages...")
        os.system("pip install pillow matplotlib")
        from PIL import Image, ImageDraw, ImageFont
        import matplotlib.pyplot as plt
        import matplotlib.patches as patches

def visualize_ocr_results(image_path: str, json_path: str, show_translations: bool = True):
    """Create visualization of OCR results with bounding boxes and text overlays."""
    
    # Install dependencies if needed
    install_dependencies()
    from PIL import Image, ImageDraw, ImageFont
    import matplotlib.pyplot as plt
    import matplotlib.patches as patches
    
    # Load the OCR results
    if not os.path.exists(json_path):
        print(f"Error: JSON file '{json_path}' not found")
        return
    
    with open(json_path, 'r', encoding='utf-8') as f:
        ocr_data = json.load(f)
    
    # Load the image
    if not os.path.exists(image_path):
        print(f"Error: Image file '{image_path}' not found")
        return
    
    image = Image.open(image_path)
    
    # Create figure with subplots
    fig, axes = plt.subplots(1, 2 if show_translations else 1, figsize=(20, 10))
    if not show_translations:
        axes = [axes]
    
    # Original image with corrected text
    axes[0].imshow(image)
    axes[0].set_title('Original Text (Corrected)', fontsize=14, fontweight='bold')
    axes[0].axis('off')
    
    # Add bounding boxes and corrected text
    for element in ocr_data.get('elements', []):
        bbox = element['bounding_box']
        corrected_text = element['corrected_text']
        confidence = element['confidence']
        
        # Create rectangle patch
        # Convert bounding box to matplotlib format
        x_coords = [point[0] for point in bbox]
        y_coords = [point[1] for point in bbox]
        x_min, x_max = min(x_coords), max(x_coords)
        y_min, y_max = min(y_coords), max(y_coords)
        
        width = x_max - x_min
        height = y_max - y_min
        
        # Color based on confidence
        color_map = {'high': 'green', 'medium': 'orange', 'low': 'red', 'unknown': 'gray'}
        color = color_map.get(confidence, 'gray')
        
        rect = patches.Rectangle((x_min, y_min), width, height, 
                               linewidth=2, edgecolor=color, facecolor='none', alpha=0.7)
        axes[0].add_patch(rect)
        
        # Add text label
        axes[0].text(x_min, y_min - 5, corrected_text, 
                    fontsize=8, color=color, fontweight='bold',
                    bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
    
    if show_translations:
        # Translation overlay
        axes[1].imshow(image)
        axes[1].set_title('English Translation', fontsize=14, fontweight='bold')
        axes[1].axis('off')
        
        # Add bounding boxes and translations
        for element in ocr_data.get('elements', []):
            bbox = element['bounding_box']
            translation = element['english_translation']
            confidence = element['confidence']
            
            # Create rectangle patch
            x_coords = [point[0] for point in bbox]
            y_coords = [point[1] for point in bbox]
            x_min, x_max = min(x_coords), max(x_coords)
            y_min, y_max = min(y_coords), max(y_coords)
            
            width = x_max - x_min
            height = y_max - y_min
            
            # Color based on confidence
            color_map = {'high': 'blue', 'medium': 'purple', 'low': 'red', 'unknown': 'gray'}
            color = color_map.get(confidence, 'gray')
            
            rect = patches.Rectangle((x_min, y_min), width, height, 
                                   linewidth=2, edgecolor=color, facecolor='none', alpha=0.7)
            axes[1].add_patch(rect)
            
            # Add translation label
            axes[1].text(x_min, y_min - 5, translation, 
                        fontsize=8, color=color, fontweight='bold',
                        bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
    
    # Add legend
    legend_elements = [
        patches.Patch(color='green', label='High Confidence'),
        patches.Patch(color='orange', label='Medium Confidence'), 
        patches.Patch(color='red', label='Low Confidence'),
        patches.Patch(color='gray', label='Unknown Confidence')
    ]
    
    fig.legend(handles=legend_elements, loc='upper center', bbox_to_anchor=(0.5, 0.02), ncol=4)
    
    # Add summary information
    detected_lang = ocr_data.get('detected_language', 'Unknown')
    total_elements = ocr_data.get('total_elements', 0)
    cost_info = ocr_data.get('cost_breakdown', {})
    total_cost = cost_info.get('total_usd', 0)
    
    fig.suptitle(f'OCR Results - Language: {detected_lang} | Elements: {total_elements} | Cost: ${total_cost:.6f}', 
                fontsize=16, fontweight='bold')
    
    plt.tight_layout()
    
    # Save the visualization
    output_path = f"{Path(image_path).stem}_ocr_visualization.png"
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"Visualization saved to: {output_path}")
    
    # Show the plot
    plt.show()

def print_text_summary(json_path: str):
    """Print a text summary of the OCR results."""
    with open(json_path, 'r', encoding='utf-8') as f:
        ocr_data = json.load(f)
    
    print("OCR Results Summary")
    print("=" * 50)
    
    print(f"Detected Language: {ocr_data.get('detected_language', 'Unknown')}")
    print(f"Total Elements: {ocr_data.get('total_elements', 0)}")
    
    cost_info = ocr_data.get('cost_breakdown', {})
    if cost_info:
        print(f"Total Cost: ${cost_info.get('total_usd', 0):.6f}")
    
    print(f"\nOriginal Text:\n{ocr_data.get('full_corrected_text', '')}")
    print(f"\nEnglish Translation:\n{ocr_data.get('full_english_translation', '')}")
    
    print(f"\nNotes: {ocr_data.get('gemini_notes', '')}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python visualize_ocr.py <json_file> [image_file] [--no-translations]")
        print("Example: python visualize_ocr.py jar_ocr_results.json jar.jpg")
        sys.exit(1)
    
    json_file = sys.argv[1]
    
    # Try to infer image file from JSON filename
    if len(sys.argv) > 2 and not sys.argv[2].startswith('--'):
        image_file = sys.argv[2]
    else:
        # Infer from JSON filename
        json_stem = Path(json_file).stem
        if json_stem.endswith('_ocr_results'):
            image_stem = json_stem[:-12]  # Remove '_ocr_results'
            # Look for common image extensions
            for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
                potential_image = f"{image_stem}{ext}"
                if os.path.exists(potential_image):
                    image_file = potential_image
                    break
            else:
                print(f"Could not find image file for {json_file}")
                print("Please specify the image file as the second argument")
                sys.exit(1)
        else:
            print("Could not infer image filename. Please specify it as the second argument")
            sys.exit(1)
    
    show_translations = '--no-translations' not in sys.argv
    
    print(f"Visualizing OCR results from: {json_file}")
    print(f"Using image: {image_file}")
    
    # Print text summary
    print_text_summary(json_file)
    
    # Create visualization
    visualize_ocr_results(image_file, json_file, show_translations)
