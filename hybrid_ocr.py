#!/usr/bin/env python3
"""
Hybrid OCR using Google Cloud Vision for bounding boxes and Gemini for better text recognition.
"""
import os
import sys
import json
import base64
from pathlib import Path
from typing import Dict, List, Any

def setup_credentials():
    """Set up Google Cloud credentials if not already configured."""
    if os.getenv('GOOGLE_APPLICATION_CREDENTIALS'):
        return True
    
    possible_files = [
        'google-cloud-key.json',
        'service-account-key.json', 
        'credentials.json'
    ]
    
    for file_name in possible_files:
        if os.path.exists(file_name):
            os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = file_name
            print(f"Using credentials from: {file_name}")
            return True
    
    print("ERROR: No Google Cloud credentials found!")
    return False

def get_vision_data(image_path: str) -> Dict[str, Any]:
    """Get text detection data from Google Cloud Vision API."""
    if not setup_credentials():
        return None
    
    try:
        from google.cloud import vision
        client = vision.ImageAnnotatorClient()
    except Exception as e:
        print(f"Error creating Vision client: {e}")
        return None

    if not os.path.exists(image_path):
        print(f"Error: Image file '{image_path}' not found")
        return None

    with open(image_path, "rb") as image_file:
        content = image_file.read()

    image = vision.Image(content=content)

    try:
        response = client.text_detection(image=image)
        
        if response.error.message:
            raise Exception(f"{response.error.message}")
        
        texts = response.text_annotations
        if not texts:
            return {"full_text": "", "elements": []}
        
        # Extract individual text elements with bounding boxes
        elements = []
        for text in texts[1:]:  # Skip first element (full text)
            vertices = [(vertex.x, vertex.y) for vertex in text.bounding_poly.vertices]
            elements.append({
                "text": text.description,
                "bounding_box": vertices
            })
        
        return {
            "full_text": texts[0].description if texts else "",
            "elements": elements
        }
        
    except Exception as e:
        print(f"Error during Vision API call: {e}")
        return None

def encode_image_base64(image_path: str) -> str:
    """Encode image to base64 for Gemini API."""
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')

def call_gemini_api(image_path: str, vision_data: Dict[str, Any]) -> Dict[str, Any]:
    """Call Gemini API to get corrected text recognition."""
    
    # Check for Gemini API key
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("ERROR: GEMINI_API_KEY environment variable not set!")
        print("Please set your Gemini API key: export GEMINI_API_KEY=your_api_key_here")
        print("Get your API key from: https://makersuite.google.com/app/apikey")
        return None
    
    try:
        import requests
    except ImportError:
        print("Installing requests library...")
        os.system("pip install requests")
        import requests
    
    # Encode image
    image_base64 = encode_image_base64(image_path)
    
    # Prepare the prompt
    vision_elements = vision_data.get("elements", [])
    vision_text_list = [elem["text"] for elem in vision_elements]
    
    prompt = f"""
You are an expert OCR correction system. I have an image with text that was processed by Google Cloud Vision API, but the text recognition may have errors.

Here is the text that Google Cloud Vision detected:
{json.dumps(vision_text_list, indent=2)}

Please analyze the image and provide corrected text recognition. For each text element detected by Vision API, provide the corrected version.

Return your response as a JSON object with this exact structure:
{{
  "corrected_elements": [
    {{
      "original_text": "text_from_vision_api",
      "corrected_text": "your_corrected_version",
      "confidence": "high|medium|low"
    }}
  ],
  "full_corrected_text": "complete corrected text from the image",
  "notes": "any observations about corrections made"
}}

Focus on:
1. Correcting OCR errors (misread characters, wrong words)
2. Fixing spacing and formatting issues
3. Handling special characters and diacritics correctly
4. Maintaining the original structure and meaning
"""

    # Prepare API request
    url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={api_key}"
    
    headers = {
        "Content-Type": "application/json"
    }
    
    data = {
        "contents": [{
            "parts": [
                {"text": prompt},
                {
                    "inline_data": {
                        "mime_type": "image/jpeg",
                        "data": image_base64
                    }
                }
            ]
        }],
        "generationConfig": {
            "response_mime_type": "application/json"
        }
    }
    
    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        
        result = response.json()
        
        if 'candidates' in result and len(result['candidates']) > 0:
            content = result['candidates'][0]['content']['parts'][0]['text']
            return json.loads(content)
        else:
            print("No response from Gemini API")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"Error calling Gemini API: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"Error parsing Gemini response: {e}")
        print(f"Raw response: {content}")
        return None

def combine_results(vision_data: Dict[str, Any], gemini_data: Dict[str, Any]) -> Dict[str, Any]:
    """Combine Vision API bounding boxes with Gemini's corrected text."""
    if not vision_data or not gemini_data:
        return None
    
    combined_elements = []
    vision_elements = vision_data.get("elements", [])
    corrected_elements = gemini_data.get("corrected_elements", [])
    
    # Create a mapping of original to corrected text
    correction_map = {}
    for correction in corrected_elements:
        correction_map[correction["original_text"]] = correction
    
    # Combine bounding boxes with corrected text
    for vision_elem in vision_elements:
        original_text = vision_elem["text"]
        correction = correction_map.get(original_text, {})
        
        combined_elements.append({
            "original_text": original_text,
            "corrected_text": correction.get("corrected_text", original_text),
            "confidence": correction.get("confidence", "unknown"),
            "bounding_box": vision_elem["bounding_box"]
        })
    
    return {
        "elements": combined_elements,
        "full_corrected_text": gemini_data.get("full_corrected_text", ""),
        "gemini_notes": gemini_data.get("notes", ""),
        "total_elements": len(combined_elements)
    }

def hybrid_ocr(image_path: str) -> Dict[str, Any]:
    """Perform hybrid OCR using both Vision API and Gemini."""
    print(f"Processing image: {image_path}")
    print("=" * 60)
    
    # Step 1: Get Vision API data
    print("Step 1: Getting bounding boxes from Google Cloud Vision...")
    vision_data = get_vision_data(image_path)
    if not vision_data:
        return None
    
    print(f"Vision API detected {len(vision_data['elements'])} text elements")
    
    # Step 2: Get Gemini corrections
    print("Step 2: Getting text corrections from Gemini...")
    gemini_data = call_gemini_api(image_path, vision_data)
    if not gemini_data:
        print("Failed to get Gemini corrections, using Vision API results only")
        return vision_data
    
    # Step 3: Combine results
    print("Step 3: Combining results...")
    combined_data = combine_results(vision_data, gemini_data)
    
    return combined_data

if __name__ == "__main__":
    image_path = 'jar.jpg'
    if len(sys.argv) > 1:
        image_path = sys.argv[1]
    
    result = hybrid_ocr(image_path)
    
    if result:
        print("\n" + "=" * 60)
        print("HYBRID OCR RESULTS")
        print("=" * 60)
        
        # Save results to JSON file
        output_file = f"{Path(image_path).stem}_ocr_results.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"\nFull results saved to: {output_file}")
        
        # Display summary
        if "full_corrected_text" in result:
            print(f"\nCorrected Text:\n{result['full_corrected_text']}")
        
        if "gemini_notes" in result:
            print(f"\nGemini Notes: {result['gemini_notes']}")
        
        print(f"\nTotal elements processed: {result.get('total_elements', 0)}")
        
        # Show first few corrections as examples
        elements = result.get('elements', [])
        if elements:
            print("\nExample corrections:")
            for i, elem in enumerate(elements[:5]):
                if elem['original_text'] != elem['corrected_text']:
                    print(f"  '{elem['original_text']}' → '{elem['corrected_text']}' ({elem['confidence']})")
    else:
        print("Failed to process image")
