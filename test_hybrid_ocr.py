#!/usr/bin/env python3
"""
Test script to demonstrate the hybrid OCR system.
"""
import os

def test_setup():
    """Test if all required components are set up."""
    print("Testing Hybrid OCR Setup")
    print("=" * 40)
    
    # Check Google Cloud Vision credentials
    vision_ok = False
    if os.getenv('GOOGLE_APPLICATION_CREDENTIALS'):
        print("✅ Google Cloud credentials: Found")
        vision_ok = True
    elif any(os.path.exists(f) for f in ['google-cloud-key.json', 'service-account-key.json', 'credentials.json']):
        print("✅ Google Cloud credentials: Found in project directory")
        vision_ok = True
    else:
        print("❌ Google Cloud credentials: Not found")
    
    # Check Gemini API key
    gemini_ok = False
    if os.getenv('GEMINI_API_KEY'):
        key = os.getenv('GEMINI_API_KEY')
        print(f"✅ Gemini API key: Found ({key[:10]}...)")
        gemini_ok = True
    else:
        print("❌ Gemini API key: Not found")
    
    # Check if image exists
    image_ok = os.path.exists('jar.jpg')
    if image_ok:
        print("✅ Test image (jar.jpg): Found")
    else:
        print("❌ Test image (jar.jpg): Not found")
    
    print("\n" + "=" * 40)
    
    if vision_ok and gemini_ok and image_ok:
        print("🎉 All components ready! You can run the hybrid OCR.")
        print("Run: python hybrid_ocr.py")
        return True
    else:
        print("⚠️  Setup incomplete. Please complete the missing components:")
        if not vision_ok:
            print("   - Set up Google Cloud Vision credentials")
        if not gemini_ok:
            print("   - Get Gemini API key from https://makersuite.google.com/app/apikey")
            print("   - Set it with: export GEMINI_API_KEY=your_key")
        if not image_ok:
            print("   - Make sure jar.jpg exists in the current directory")
        return False

def show_expected_workflow():
    """Show what the hybrid OCR workflow will do."""
    print("\nHybrid OCR Workflow:")
    print("=" * 40)
    print("1. 📷 Google Cloud Vision API:")
    print("   - Extracts text with precise bounding boxes")
    print("   - Provides spatial coordinates for each text element")
    print("   - May have some OCR recognition errors")
    
    print("\n2. 🧠 Gemini AI:")
    print("   - Analyzes the same image with superior text recognition")
    print("   - Corrects OCR errors from Vision API")
    print("   - Provides confidence levels for corrections")
    
    print("\n3. 🔄 Hybrid Combination:")
    print("   - Keeps precise bounding boxes from Vision API")
    print("   - Uses corrected text from Gemini")
    print("   - Outputs structured JSON with both spatial and textual data")
    
    print("\n4. 📊 Output Format:")
    print("   - JSON file with corrected text + bounding boxes")
    print("   - Confidence levels for each correction")
    print("   - Full corrected text reconstruction")

if __name__ == "__main__":
    ready = test_setup()
    show_expected_workflow()
    
    if ready:
        print("\n🚀 Ready to run hybrid OCR!")
        print("Execute: python hybrid_ocr.py jar.jpg")
    else:
        print("\n📋 Complete the setup steps above, then run this test again.")
